<?php
// phpcs:disable WordPress.PHP.DevelopmentFunctions.error_log_var_export
namespace Automattic\Jetpack\Autoloader;
if (!defined('ABSPATH')) exit;
class ManifestGenerator {
 public static function buildManifest( $autoloaderType, $fileName, $content ) {
 if ( empty( $content ) ) {
 return null;
 }
 switch ( $autoloaderType ) {
 case 'classmap':
 case 'files':
 return self::buildStandardManifest( $fileName, $content );
 case 'psr-4':
 return self::buildPsr4Manifest( $fileName, $content );
 }
 throw new \InvalidArgumentException( 'An invalid manifest type of ' . $autoloaderType . ' was passed!' );
 }
 private static function buildStandardManifest( $fileName, $manifestData ) {
 $fileContent = PHP_EOL;
 foreach ( $manifestData as $key => $data ) {
 $key = var_export( $key, true );
 $versionCode = var_export( $data['version'], true );
 $fileContent .= <<<MANIFEST_CODE
 $key => array(
 'version' => $versionCode,
 'path' => {$data['path']}
 ),
MANIFEST_CODE;
 $fileContent .= PHP_EOL;
 }
 return self::buildFile( $fileName, $fileContent );
 }
 private static function buildPsr4Manifest( $fileName, $namespaces ) {
 $fileContent = PHP_EOL;
 foreach ( $namespaces as $namespace => $data ) {
 $namespaceCode = var_export( $namespace, true );
 $versionCode = var_export( $data['version'], true );
 $pathCode = 'array( ' . implode( ', ', $data['path'] ) . ' )';
 $fileContent .= <<<MANIFEST_CODE
 $namespaceCode => array(
 'version' => $versionCode,
 'path' => $pathCode
 ),
MANIFEST_CODE;
 $fileContent .= PHP_EOL;
 }
 return self::buildFile( $fileName, $fileContent );
 }
 private static function buildFile( $fileName, $content ) {
 return <<<INCLUDE_FILE
<?php
// This file `$fileName` was auto generated by automattic/jetpack-autoloader.
\$vendorDir = dirname(__DIR__);
\$baseDir = dirname(\$vendorDir);
return array($content);
INCLUDE_FILE;
 }
}
